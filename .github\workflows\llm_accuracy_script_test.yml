name: Test LLM Accuracy Scripts

# Optimized for fast CI runtime:
# - Uses calibration datasets (smaller) for faster processing
# - Tests accuracy evaluation scripts without downloading models
# - Uses HuggingFace model identifiers directly in checkpoint paths

on:
  pull_request:
    branches: [ "master", "dev" ]
    paths:
      - 'language/**/*evaluate-accuracy.py'
      - 'language/**/*eval_accuracy.py'
      - 'language/**/*evaluate_mbxp.py'
      - '.github/workflows/llm_accuracy_script_test.yml'
      - '!**.md'

jobs:
  test-llama3-accuracy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.10"

    - name: Install dependencies as specified in Llama3.1 README
      run: |
        python -m pip install --upgrade pip
        # Install requirements.txt as specified in Llama3.1 README (excluding vllm for CI)
        cd language/llama3.1-405b
        pip install numpy==1.24.3 transformers==4.46.2 nltk==3.8.1 evaluate==0.4.0 absl-py==1.4.0 rouge-score==0.1.2 sentencepiece==0.2.0 accelerate==0.21.0 pybind11==2.10.4
        python -c "import nltk; nltk.download('punkt'); nltk.download('punkt_tab')"
        # Install loadgen as specified in README
        cd ../../loadgen
        pip install -e .
        cd ../language/llama3.1-405b

    - name: Install rclone for Llama3 dataset download
      run: |
        curl https://rclone.org/install.sh | sudo bash
        rclone config create mlc-inference s3 provider=Cloudflare access_key_id=f65ba5eef400db161ea49967de89f47b secret_access_key=fbea333914c292b854f14d3fe232bad6c5407bf0ab1bebf78833c2b359bdfd2b endpoint=https://c2686074cb2caf5cbaf6d134bdba8b47.r2.cloudflarestorage.com

    - name: Download Llama3 calibration dataset as specified in README
      run: |
        mkdir -p tests/fixtures/llama3
        cd tests/fixtures/llama3

        # Try MLCommons method first as specified in README
        pip install mlc-scripts
        if mlc pull repo mlcommons@mlperf-automations --branch=dev && mlcr get,dataset,mlperf,inference,llama3,_calibration --outdirname=. -j; then
          echo "MLCommons download successful"
          DATASET_FILE=$(find . -name "*.pkl" | head -1)
        else
          echo "MLCommons download failed, using rclone fallback as specified in README"
          # Fallback to rclone method as specified in README
          rclone copy mlc-inference:mlcommons-inference-wg-public/llama3.1_405b/mlperf_llama3.1_405b_calibration_dataset_512_processed_fp16_eval.pkl ./ -P
          DATASET_FILE="mlperf_llama3.1_405b_calibration_dataset_512_processed_fp16_eval.pkl"
        fi

        # Use the dataset file
        if [ -n "$DATASET_FILE" ] && [ -f "$DATASET_FILE" ]; then
          cp "$DATASET_FILE" dataset.pkl
          echo "Dataset file created: dataset.pkl"
        else
          echo "ERROR: No dataset file found"
          exit 1
        fi

    - name: Test Llama3.1 accuracy script
      run: |
        cd language/llama3.1-405b
        # Verify dependencies are properly installed
        python -c "import transformers, nltk, evaluate, numpy; print('All dependencies imported successfully')"

        # Use calibration-appropriate accuracy log (10 samples for fast CI)
        python evaluate-accuracy.py --checkpoint-path  microsoft/DialoGPT-medium \
          --mlperf-accuracy-file ../../.github/assets/accuracy_evaluation/llama3.1-405b/mlperf_log_accuracy_calibration.json \
          --dataset-file ../../tests/fixtures/llama3/dataset.pkl \
          --dtype int32

    
  test-mixtral-accuracy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.10"

    - name: Install system dependencies for mxeval
      run: |
        sudo apt-get update
        sudo apt-get install -y build-essential curl wget git

    - name: Install minimal dependencies for Mixtral evaluate-accuracy.py
      run: |
        python -m pip install --upgrade pip
        # Install essential dependencies for evaluate-accuracy.py
        pip install transformers==4.31.0 nltk==3.8.1 evaluate==0.4.0 absl-py==1.4.0 rouge-score==0.1.2 sentencepiece==0.1.99 accelerate==0.21.0
        pip install pandas numpy tqdm fire
        python -c "import nltk; nltk.download('punkt'); nltk.download('punkt_tab')"
        # Install mxeval for MBXP evaluation using clone method
        cd language/mixtral-8x7b
        git clone https://github.com/amazon-science/mxeval.git
        cd mxeval
        git checkout e09974f990eeaf0c0e8f2b5eaff4be66effb2c86
        # Add to Python path manually to avoid entry point issues
        export PYTHONPATH="${PWD}:${PYTHONPATH}"
        cd ..




    - name: Download Mixtral calibration dataset
      run: |
        mkdir -p tests/fixtures/mixtral
        cd tests/fixtures/mixtral

        # Download calibration dataset for faster CI
        wget "https://inference.mlcommons-storage.org/mixtral_8x7b%2F2024.06.06_mixtral_15k_calibration_v4.pkl"

        # The file is downloaded with URL encoding in the name, so check for the actual filename
        DOWNLOADED_FILE=$(ls mixtral_8x7b* 2>/dev/null | head -1)
        if [ -n "$DOWNLOADED_FILE" ]; then
          mv "$DOWNLOADED_FILE" dataset.pkl
          echo "Dataset file created: dataset.pkl"
        else
          echo "ERROR: Expected dataset file not found"
          echo "Available files:"
          ls -la
          exit 1
        fi

    - name: Test Mixtral accuracy script
      run: |
        cd language/mixtral-8x7b
        # Set PYTHONPATH to include mxeval if it was installed manually
        export PYTHONPATH="$(pwd)/mxeval:${PYTHONPATH}"

        # Use calibration-appropriate accuracy log (10 samples for fast CI)
        # Add --n_workers parameter as shown in README
        python evaluate-accuracy.py --checkpoint-path  microsoft/DialoGPT-medium \
          --mlperf-accuracy-file ../../.github/assets/accuracy_evaluation/mixtral-8x7b/mlperf_log_accuracy_calibration.json \
          --dataset-file ../../tests/fixtures/mixtral/dataset.pkl \
          --dtype int32 \
          --n_workers 2

  test-llama2-accuracy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.10"

    - name: Install dependencies as specified in Llama2 README
      run: |
        python -m pip install --upgrade pip
        # Install exact dependencies as specified in Llama2 README
        pip install transformers==4.31.0 nltk==3.8.1 evaluate==0.4.0 absl-py==1.4.0 rouge-score==0.1.2 sentencepiece==0.1.99 accelerate==0.21.0
        # Additional dependencies for CI - install numpy first to avoid conflicts
        pip install numpy==1.24.3 pandas tqdm protobuf
        python -c "import nltk; nltk.download('punkt'); nltk.download('punkt_tab')"
        # Install loadgen as specified in README
        cd loadgen
        pip install .
        cd ../language/llama2-70b




    - name: Install rclone for dataset download
      run: |
        curl https://rclone.org/install.sh | sudo bash
        rclone config create mlc-inference s3 provider=Cloudflare access_key_id=f65ba5eef400db161ea49967de89f47b secret_access_key=fbea333914c292b854f14d3fe232bad6c5407bf0ab1bebf78833c2b359bdfd2b endpoint=https://c2686074cb2caf5cbaf6d134bdba8b47.r2.cloudflarestorage.com

    - name: Download Llama2 dataset
      run: |
        mkdir -p tests/fixtures/llama2
        cd tests/fixtures/llama2

        # Download dataset using rclone
        rclone copy mlc-inference:mlcommons-inference-wg-public/open_orca/open_orca_gpt4_tokenized_llama.sampled_24576.pkl.gz ./ -P

        # Decompress the dataset file
        DATASET_FILE="open_orca_gpt4_tokenized_llama.sampled_24576.pkl.gz"
        if [ -f "$DATASET_FILE" ]; then
          gunzip "$DATASET_FILE"
          mv "open_orca_gpt4_tokenized_llama.sampled_24576.pkl" dataset.pkl
          echo "Dataset file created: dataset.pkl"
        else
          echo "ERROR: Expected dataset file not found"
          exit 1
        fi

    - name: Test Llama2 accuracy script
      run: |
        cd language/llama2-70b
        # Verify NumPy installation before running
        python -c "import numpy; print(f'NumPy version: {numpy.__version__}')"

        # Use calibration-appropriate accuracy log (10 samples for fast CI)
        python evaluate-accuracy.py --checkpoint-path  microsoft/DialoGPT-medium \
          --mlperf-accuracy-file ../../.github/assets/accuracy_evaluation/llama2-70b/mlperf_log_accuracy_calibration.json \
          --dataset-file ../../tests/fixtures/llama2/dataset.pkl \
          --dtype int32

  test-deepseek-accuracy:
    runs-on: ubuntu-latest
    env:
      HF_TOKEN: "*************************************"
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.10"

    - name: Install dependencies as specified in DeepSeek README
      run: |
        python -m pip install --upgrade pip
        # Install basic dependencies for DeepSeek evaluation
        python -m pip install transformers pandas numpy rouge-score nltk evaluate absl-py sentencepiece accelerate tqdm
        python -c "import nltk; nltk.download('punkt'); nltk.download('punkt_tab')"




    - name: Install rclone for dataset download
      run: |
        curl https://rclone.org/install.sh | sudo bash
        rclone config create mlc-inference s3 provider=Cloudflare access_key_id=f65ba5eef400db161ea49967de89f47b secret_access_key=fbea333914c292b854f14d3fe232bad6c5407bf0ab1bebf78833c2b359bdfd2b endpoint=https://c2686074cb2caf5cbaf6d134bdba8b47.r2.cloudflarestorage.com

    - name: Download DeepSeek calibration dataset
      run: |
        mkdir -p tests/fixtures/deepseek
        cd tests/fixtures/deepseek

        # Download calibration dataset (500 samples) for faster CI
        rclone copy mlc-inference:mlcommons-inference-wg-public/deepseek_r1/mlperf_deepseek_r1_calibration_dataset_500_fp8_eval.pkl ./ -P

        # Use the downloaded dataset file
        if [ -f "mlperf_deepseek_r1_calibration_dataset_500_fp8_eval.pkl" ]; then
          mv "mlperf_deepseek_r1_calibration_dataset_500_fp8_eval.pkl" dataset.pkl
          echo "Dataset file created: dataset.pkl"
        else
          echo "ERROR: Expected dataset file not found"
          exit 1
        fi

    - name: Test DeepSeek accuracy script (basic import test)
      run: |
        cd language/deepseek-r1
        # DeepSeek model is automatically downloaded as per README
        python -c "
        import eval_accuracy
        print('DeepSeek eval_accuracy.py imports successfully')
        # Test basic functionality without external dependencies
        try:
            result = eval_accuracy.parse_multiple_choice('The answer is A', 'D')
            print(f'Multiple choice parsing test: {result}')
        except Exception as e:
            print(f'Expected error due to missing dependencies: {e}')
        "

